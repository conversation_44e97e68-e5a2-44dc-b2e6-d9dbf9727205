# ABDM API Connectivity Improvements

## Overview

This document outlines the improvements made to the ABDM API connectivity in `milestone.two.helper.js` to address connection timeout errors and improve overall reliability.

## Issues Addressed

### Original Problem
- **Connection Timeout Error**: `ConnectTimeoutError: Connect Timeout Error (attempted address: dev.abdm.gov.in:443, timeout: 10000ms)`
- **Error Code**: `UND_ERR_CONNECT_TIMEOUT`
- **Function**: `informHUI` calling `fetchAbhaApi`

## Improvements Implemented

### 1. Enhanced Timeout Configuration
- **Increased base timeout**: From 30s to 60s (90s for critical operations like `informHUI`)
- **Increased max retries**: From 3 to 5 attempts
- **Increased base delay**: From 1s to 2s (3s for critical operations)

### 2. Improved Retry Logic
- **Exponential backoff with jitter**: Prevents thundering herd problem
- **Enhanced error detection**: Added support for more error codes:
  - `UND_ERR_CONNECT_TIMEOUT`
  - `ECONNABORTED`
  - `ConnectTimeoutError`
  - Network-related errors

### 3. Better Error Handling
- **Detailed error logging**: Includes error code, name, stack trace, and attempt number
- **Graceful degradation**: `informHUI` now returns error response instead of throwing
- **Error categorization**: Distinguishes between retryable and non-retryable errors

### 4. Network Diagnostics
- **DNS resolution check**: Verifies hostname resolution before making requests
- **Connectivity testing**: Basic connectivity test to ABDM endpoints
- **Diagnostic logging**: Detailed logs for troubleshooting

### 5. Enhanced Request Configuration
- **Keep-alive connections**: Improves connection reuse
- **User-Agent header**: Identifies requests as coming from Healtether
- **Cache-Control**: Prevents caching issues
- **Better request logging**: Logs request body and headers for debugging

## Code Changes

### Modified Functions

#### `fetchAbhaApi`
- Increased timeout and retry limits
- Added DNS resolution check on first attempt
- Enhanced error handling with more error codes
- Added jitter to retry delays
- Improved logging and diagnostics

#### `informHUI`
- Wrapped in try-catch for better error handling
- Custom retry configuration for critical operations
- Returns error response instead of throwing exceptions
- Enhanced logging for debugging

### New Functions

#### `checkNetworkConnectivity`
- Tests DNS resolution for ABDM hostname
- Provides diagnostic information for network issues

#### `testConnectivity`
- Performs basic HTTP connectivity test
- Helps identify network-level issues

#### `testAbdmConnectivity`
- Comprehensive connectivity test function
- Tests DNS, connectivity, and token generation
- Useful for debugging and monitoring

## Usage

### Running Connectivity Test
```bash
node test-abdm-connectivity.js
```

### Using Improved Functions
The existing functions work the same way but with better error handling:

```javascript
// This will now retry up to 5 times with exponential backoff
const result = await informHUI(notificationBody);

// Check if the operation succeeded
if (result.isSuccess) {
  console.log("Success:", result.response);
} else {
  console.error("Failed:", result.response.error);
  console.error("Details:", result.response.details);
}
```

## Monitoring and Debugging

### Log Messages to Watch For
- `DNS resolution failed for dev.abdm.gov.in` - Network/DNS issues
- `ConnectTimeoutError detected. Retrying in Xms` - Connection timeouts being retried
- `fetchAbhaApi failed after X attempts` - All retries exhausted

### Environment Variables to Check
- `ABHA_M2_BASE_URL` - Should be `https://dev.abdm.gov.in`
- `CLIENT_ID` - ABDM client ID
- `CLIENT_SECRET` - ABDM client secret

## Benefits

1. **Increased Reliability**: More retries and better error handling
2. **Better Diagnostics**: Detailed logging and connectivity tests
3. **Graceful Degradation**: Functions return errors instead of crashing
4. **Network Resilience**: Handles various network conditions
5. **Debugging Support**: Comprehensive logging for troubleshooting

## Next Steps

1. **Monitor Performance**: Watch logs for retry patterns and success rates
2. **Adjust Timeouts**: Fine-tune timeout values based on production performance
3. **Add Metrics**: Consider adding metrics collection for API call success rates
4. **Circuit Breaker**: Consider implementing circuit breaker pattern for repeated failures

## Testing

Run the connectivity test to verify improvements:
```bash
cd Healtether.Communications
node test-abdm-connectivity.js
```

This will test DNS resolution, basic connectivity, token generation, and the `informHUI` function with the new error handling.
