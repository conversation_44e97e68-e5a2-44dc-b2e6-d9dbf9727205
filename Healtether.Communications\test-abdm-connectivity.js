#!/usr/bin/env node

/**
 * ABDM Connectivity Test Script
 * 
 * This script tests the improved ABDM API connectivity with enhanced error handling,
 * retry logic, and network diagnostics.
 * 
 * Usage: node test-abdm-connectivity.js
 */

import { testAbdmConnectivity, informHUI } from './helper/abha/milestone.two.helper.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function runConnectivityTest() {
  console.log("🚀 Starting ABDM Connectivity Test...\n");
  
  try {
    // Run the connectivity test
    const result = await testAbdmConnectivity();
    
    console.log("\n=== Test Results ===");
    console.log("Overall Success:", result.success);
    console.log("DNS Resolution:", result.dns?.success ? "✅ PASS" : "❌ FAIL");
    console.log("Basic Connectivity:", result.connectivity?.success ? "✅ PASS" : "❌ FAIL");
    console.log("Token Generation:", result.token === "SUCCESS" ? "✅ PASS" : "❌ FAIL");
    
    if (!result.success) {
      console.log("\n❌ Issues detected:");
      if (!result.dns?.success) {
        console.log(`  - DNS: ${result.dns?.error}`);
      }
      if (!result.connectivity?.success) {
        console.log(`  - Connectivity: ${result.connectivity?.error}`);
      }
      if (result.token !== "SUCCESS") {
        console.log(`  - Token: ${result.token}`);
      }
    }
    
    return result.success;
    
  } catch (error) {
    console.error("❌ Connectivity test failed:", error.message);
    return false;
  }
}

async function testInformHUI() {
  console.log("\n🧪 Testing informHUI function with improved error handling...\n");
  
  try {
    // Create a test payload for informHUI
    const testPayload = {
      notification: {
        consentId: "test-consent-id",
        status: "GRANTED",
        timestamp: new Date().toISOString()
      }
    };
    
    console.log("Calling informHUI with test payload...");
    const result = await informHUI(testPayload);
    
    console.log("informHUI Result:");
    console.log("- Success:", result.isSuccess);
    console.log("- Response:", result.response);
    
    if (!result.isSuccess && result.response?.error) {
      console.log("- Error Details:", result.response.details);
    }
    
    return result.isSuccess;
    
  } catch (error) {
    console.error("❌ informHUI test failed:", error.message);
    return false;
  }
}

async function main() {
  console.log("🔧 ABDM API Connectivity & Error Handling Test");
  console.log("=" .repeat(50));
  
  // Test basic connectivity
  const connectivityPassed = await runConnectivityTest();
  
  // Test the specific function that was failing
  const informHUIPassed = await testInformHUI();
  
  console.log("\n" + "=".repeat(50));
  console.log("📊 Final Results:");
  console.log(`Connectivity Test: ${connectivityPassed ? "✅ PASS" : "❌ FAIL"}`);
  console.log(`informHUI Test: ${informHUIPassed ? "✅ PASS" : "❌ FAIL"}`);
  
  if (connectivityPassed && informHUIPassed) {
    console.log("\n🎉 All tests passed! The ABDM connectivity improvements are working.");
  } else {
    console.log("\n⚠️  Some tests failed. Check the logs above for details.");
    console.log("\nTroubleshooting tips:");
    console.log("1. Check your internet connection");
    console.log("2. Verify ABDM_M2_BASE_URL environment variable");
    console.log("3. Ensure CLIENT_ID and CLIENT_SECRET are correct");
    console.log("4. Check if dev.abdm.gov.in is accessible from your network");
  }
  
  process.exit(connectivityPassed && informHUIPassed ? 0 : 1);
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the test
main().catch(error => {
  console.error("❌ Test script failed:", error);
  process.exit(1);
});
