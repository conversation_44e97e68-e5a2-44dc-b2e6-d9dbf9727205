{"resourceType": "Bundle", "id": "e62c3d9e-2b6c-44b6-b7fe-6fc7e94f026a", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:14:22.851+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/DocumentBundle"], "security": [{"system": "http://terminology.hl7.org/CodeSystem/v3-Confidentiality", "code": "V", "display": "very restricted"}]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "type": "document", "timestamp": "2025-06-04T05:14:22.851+05:30", "entry": [{"fullUrl": "urn:uuid:0d668fa8-7ac4-474f-99c0-0857218cc18f", "resource": {"resourceType": "Composition", "id": "0d668fa8-7ac4-474f-99c0-0857218cc18f", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:14:22.851+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/OPConsultRecord"]}, "identifier": {"system": "https://www.healtether.com", "value": "SBX_003515"}, "language": "en", "status": "final", "type": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}], "text": "Clinical consultation report"}, "date": "2025-06-04T05:14:22.851+05:30", "title": "Consultation Report", "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "encounter": {"reference": "urn:uuid:0bf381a3-c5ab-4b55-afeb-4fe1e5f9ddaf", "display": "Encounter"}, "author": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "attester": [{"mode": "legal", "party": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"mode": "legal", "party": {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "display": "Test"}}], "custodian": {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "display": "Test"}, "section": [{"title": "Chief complaints", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422843007", "display": "Chief complaint section"}], "text": "Chief complaint section"}, "entry": [{"reference": "urn:uuid:4bed1b2f-14b4-4ce0-821a-27ede6cc3276", "display": "Condition"}]}, {"title": "Allergies", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "722446000", "display": "Allergy record"}]}, "entry": [{"reference": "urn:uuid:b2b40d14-c67c-4b05-8b4a-6d56aa38d763", "display": "AllergyIntolerance"}]}, {"title": "FamilyHistory", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "422432008", "display": "Family history section"}]}, "entry": [{"reference": "urn:uuid:62fd637e-358b-4254-83fb-f25741d1a757", "display": "FamilyMemberHistory"}]}, {"title": "Vital Signs", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "118227000", "display": "Vital signs finding"}]}, "entry": [{"reference": "urn:uuid:e2e0dd8e-0396-48a9-ad2b-664e48a975ee", "display": "Observation"}, {"reference": "urn:uuid:6f200a75-2025-494c-9b02-965478e88829", "display": "Observation"}, {"reference": "urn:uuid:aa287ff7-0c10-4543-8d29-0eb591e0c365", "display": "Observation"}, {"reference": "urn:uuid:c85421d8-b8c9-4e57-a020-4cdca2157c06", "display": "Observation"}, {"reference": "urn:uuid:220c5779-d73d-499e-b1df-5f9c16bb7af7", "display": "Observation"}, {"reference": "urn:uuid:3991be20-fd0e-4c10-8070-0b01c0db1457", "display": "Observation"}, {"reference": "urn:uuid:e44443a9-df4f-4817-b9bf-6d390cc0aeff", "display": "Observation"}, {"reference": "urn:uuid:cfc0af06-47bc-400b-a78b-c5890bf3be6c", "display": "Observation"}]}, {"title": "Procedure", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371525003", "display": "Clinical procedure report"}]}, "entry": [{"reference": "urn:uuid:9f9052a2-e2ca-4d72-babb-84b33393ce72", "display": "Procedure"}]}, {"title": "Follow Up", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "736271009", "display": "Outpatient care plan"}]}, "entry": [{"reference": "urn:uuid:84b45ffb-7063-4436-a7d2-d2830eab92a7", "display": "Appointment"}]}, {"title": "Investigation Advice", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721963009", "display": "Order document"}]}, "entry": [{"reference": "urn:uuid:4b0a80b6-94d7-4776-8ea8-b641f4e44de7", "display": "ServiceRequest"}]}, {"title": "Medications", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "721912009", "display": "Medication summary document"}]}, "entry": [{"reference": "urn:uuid:0f32e27b-49b5-4da1-9dfe-c8064be7034b", "display": "MedicationStatement"}, {"reference": "urn:uuid:1b47767f-68ac-4c85-a6ae-9a6d2e44cfdc", "display": "MedicationRequest"}]}, {"title": "Document Reference", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "371530004", "display": "Clinical consultation report"}]}, "entry": []}]}}, {"fullUrl": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "resource": {"resourceType": "Practitioner", "id": "dc25fef5-afb0-4b56-9a1b-8ee37883be13", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:14:22.851+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Practitioner"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MD", "display": "Medical License number"}]}, "system": "https://doctor.ndhm.gov.in", "value": "1234567"}], "name": [{"use": "official", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prefix": ["Dr."]}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "address": [{"use": "home", "type": "physical", "text": "<PERSON><PERSON><PERSON> ganj", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "resource": {"resourceType": "Organization", "id": "e65e88cc-7042-4ef9-9e5b-97bdf173e453", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Organization"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "PRN", "display": "Provider number"}]}, "system": "https://facility.ndhm.gov.in", "value": "1234567"}], "name": "Test", "telecom": [{"system": "phone", "value": "**********", "use": "work"}]}}, {"fullUrl": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "resource": {"resourceType": "Patient", "id": "b5c77529-89fe-4eb1-bedf-0b4333501c4e", "meta": {"versionId": "1", "lastUpdated": "2025-06-04T05:14:22.851+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Patient"]}, "identifier": [{"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAID"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "91-1248-5708-0632"}, {"type": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/v2-0203", "code": "MR", "display": "Medical record number"}], "text": "ABHAADDRESS"}, "system": "https://abha.abdm.gov.in/abha/v3", "value": "monika_120512@sbx"}], "name": [{"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "telecom": [{"system": "phone", "value": "**********", "use": "mobile"}], "gender": "female", "birthDate": "2003-12-05", "address": [{"use": "home", "type": "physical", "text": "nimma ji ji kho , b<PERSON><PERSON> mandi gate, jiwajiganj, Gird, Gird, Gwalior, Madhya Pradesh", "city": "GWALIOR", "state": "MADHYA PRADESH", "district": "MADHYA PRADESH", "postalCode": "474001", "country": "india"}]}}, {"fullUrl": "urn:uuid:0bf381a3-c5ab-4b55-afeb-4fe1e5f9ddaf", "resource": {"resourceType": "Encounter", "id": "0bf381a3-c5ab-4b55-afeb-4fe1e5f9ddaf", "meta": {"lastUpdated": "2025-06-04T05:14:22.851+05:30", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Encounter"]}, "identifier": [{"system": "https://ndhm.in", "value": "hip1"}, {"system": "https://ndhm.in", "value": "hip2"}], "status": "finished", "class": {"system": "http://terminology.hl7.org/CodeSystem/v3-ActCode", "code": "AMB", "display": "ambulatory"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "period": {"start": "2025-06-03T20:31:39.744Z", "end": "2025-06-03T20:31:39.744Z"}, "diagnosis": [{"condition": {"reference": "urn:uuid:4bed1b2f-14b4-4ce0-821a-27ede6cc3276", "display": "Condition"}, "use": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection by Moniezia"}], "text": "(Notes: test notes)"}}]}}, {"fullUrl": "urn:uuid:b2b40d14-c67c-4b05-8b4a-6d56aa38d763", "resource": {"resourceType": "AllergyIntolerance", "id": "b2b40d14-c67c-4b05-8b4a-6d56aa38d763", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/AllergyIntolerance"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-clinical", "code": "active", "display": "active"}]}, "verificationStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/allergyintolerance-verification", "code": "confirmed", "display": "confirmed"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "75413007", "display": "Peanut"}], "text": "Peanut"}, "recordedDate": "2025-06-04T05:14:22.851+05:30", "patient": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "recorder": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "Practitioner"}, "note": [{"text": "no notes"}]}}, {"fullUrl": "urn:uuid:62fd637e-358b-4254-83fb-f25741d1a757", "resource": {"resourceType": "FamilyMemberHistory", "id": "62fd637e-358b-4254-83fb-f25741d1a757", "meta": {"versionId": "1", "lastUpdated": "2025-06-03T23:44:27.528Z", "profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/FamilyMemberHistory"]}, "patient": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "relationship": {"coding": [{"system": "http://snomed.info/sct", "code": "444018008", "display": "Person with characteristic related to subject of record"}], "text": "Person with characteristic related to subject of record"}, "status": "completed", "condition": [{"code": {"coding": [{"system": "http://snomed.info/sct", "code": "61569007", "display": "Agoraphobia without history of panic disorder (disorder)"}], "text": "Agoraphobia without history of panic disorder (disorder)"}, "note": [{"text": "test note"}], "onsetAge": {"value": 3, "unit": "Days", "system": "http://unitsofmeasure.org", "code": "a"}}]}}, {"fullUrl": "urn:uuid:e2e0dd8e-0396-48a9-ad2b-664e48a975ee", "resource": {"resourceType": "Observation", "id": "e2e0dd8e-0396-48a9-ad2b-664e48a975ee", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "85354-9", "display": "Blood pressure panel with all children optional"}], "text": "Blood pressure panel with all children optional"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "component": [{"code": {"coding": [{"system": "http://loinc.org", "code": "8462-4", "display": "Diastolic blood pressure"}], "text": "Diastolic blood pressure"}, "valueQuantity": {"value": 30, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}, {"code": {"coding": [{"system": "http://loinc.org", "code": "8480-6", "display": "Systolic blood pressure"}], "text": "Systolic blood pressure"}, "valueQuantity": {"value": 120, "unit": "mmhg", "system": "http://unitsofmeasure.org", "code": "mm[Hg]"}}]}}, {"fullUrl": "urn:uuid:6f200a75-2025-494c-9b02-965478e88829", "resource": {"resourceType": "Observation", "id": "6f200a75-2025-494c-9b02-965478e88829", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "8302-2", "display": "Body height"}], "text": "Body height"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 130, "unit": "cm", "system": "http://unitsofmeasure.org", "code": "cm"}}}, {"fullUrl": "urn:uuid:aa287ff7-0c10-4543-8d29-0eb591e0c365", "resource": {"resourceType": "Observation", "id": "aa287ff7-0c10-4543-8d29-0eb591e0c365", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "27113001", "display": "Body weight"}], "text": "Body weight"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "kg", "system": "http://unitsofmeasure.org", "code": "kg"}}}, {"fullUrl": "urn:uuid:c85421d8-b8c9-4e57-a020-4cdca2157c06", "resource": {"resourceType": "Observation", "id": "c85421d8-b8c9-4e57-a020-4cdca2157c06", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://loinc.org", "code": "61008-9", "display": "Body surface temperature"}], "text": "Body surface temperature"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 34, "unit": "degF", "system": "http://unitsofmeasure.org", "code": "[degF]"}}}, {"fullUrl": "urn:uuid:220c5779-d73d-499e-b1df-5f9c16bb7af7", "resource": {"resourceType": "Observation", "id": "220c5779-d73d-499e-b1df-5f9c16bb7af7", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "78564009", "display": "Pulse rate"}], "text": "Pulse rate"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "beats/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:3991be20-fd0e-4c10-8070-0b01c0db1457", "resource": {"resourceType": "Observation", "id": "3991be20-fd0e-4c10-8070-0b01c0db1457", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "86290005", "display": "Respiratory rate"}], "text": "Respiratory rate"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "breaths/min", "system": "http://unitsofmeasure.org", "code": "/min"}}}, {"fullUrl": "urn:uuid:e44443a9-df4f-4817-b9bf-6d390cc0aeff", "resource": {"resourceType": "Observation", "id": "e44443a9-df4f-4817-b9bf-6d390cc0aeff", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "250554003", "display": "Measurement of oxygen saturation at periphery"}], "text": "Measurement of oxygen saturation at periphery"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 40, "unit": "%", "system": "http://unitsofmeasure.org", "code": "%"}}}, {"fullUrl": "urn:uuid:cfc0af06-47bc-400b-a78b-c5890bf3be6c", "resource": {"resourceType": "Observation", "id": "cfc0af06-47bc-400b-a78b-c5890bf3be6c", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ObservationVitalSigns"]}, "status": "final", "category": [{"coding": [{"system": "http://terminology.hl7.org/CodeSystem/observation-category", "code": "vital-signs", "display": "Vital Signs"}], "text": "Vital Signs"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "269864005", "display": "Blood glucose result"}], "text": "Blood glucose result"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "type": "Patient", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "effectiveDateTime": "2025-06-04T05:14:22.851+05:30", "issued": "2025-06-03T23:44:27.528Z", "performer": [{"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "type": "Practitioner", "display": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"reference": "urn:uuid:e65e88cc-7042-4ef9-9e5b-97bdf173e453", "type": "Organization", "display": "Test"}], "valueQuantity": {"value": 50, "unit": "mg/dL", "system": "http://unitsofmeasure.org", "code": "mg/dL"}}}, {"fullUrl": "urn:uuid:84b45ffb-7063-4436-a7d2-d2830eab92a7", "resource": {"resourceType": "Appointment", "id": "84b45ffb-7063-4436-a7d2-d2830eab92a7", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Appointment"]}, "status": "booked", "description": "Follow-up consultation", "start": "2023-10-01T10:00:00+05:30", "end": "2023-10-01T11:00:00+05:30", "created": "2025-06-03T20:31:39.744Z", "serviceCategory": [{"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}], "serviceType": [{"coding": [{"system": "http://snomed.info/sct", "code": "60132005", "display": "General"}], "text": "General"}], "appointmentType": {"coding": [{"system": "http://snomed.info/sct", "code": "11429006", "display": "Consultation"}], "text": "Consultation"}, "specialty": [{"coding": [{"system": "http://snomed.info/sct", "code": "394579002", "display": "Cardiology"}], "text": "Cardiology"}], "participant": [{"actor": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "status": "accepted"}, {"actor": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "Practitioner"}, "status": "accepted"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Appointment Record</p></div>"}}}, {"fullUrl": "urn:uuid:4bed1b2f-14b4-4ce0-821a-27ede6cc3276", "resource": {"resourceType": "Condition", "id": "4bed1b2f-14b4-4ce0-821a-27ede6cc3276", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Condition"]}, "clinicalStatus": {"coding": [{"system": "http://terminology.hl7.org/CodeSystem/condition-clinical", "code": "active", "display": "Active"}]}, "code": {"coding": [{"system": "http://snomed.info/sct", "code": "67626002", "display": "Infection By Moniezia"}], "text": "(Notes: test notes)"}, "recordedDate": "2025-06-03T23:43:27.184Z", "onsetPeriod": {"start": "2025-06-03T23:43:27.184Z", "end": "2025-06-03T23:43:27.184Z"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}}}, {"fullUrl": "urn:uuid:9f9052a2-e2ca-4d72-babb-84b33393ce72", "resource": {"resourceType": "Procedure", "id": "9f9052a2-e2ca-4d72-babb-84b33393ce72", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/Procedure"]}, "status": "completed", "code": {"coding": [{"system": "http://snomed.info/sct", "code": "439719009", "display": "Pasteurization (procedure)"}], "text": "(Duration:3 Days) (Notes: test notes)"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "performedDateTime": "2023-10-01T10:30:00+05:30", "followUp": [{"coding": [{"system": "http://snomed.info/sct", "code": "281036007", "display": "Follow-up consultation"}], "text": "Follow-up consultation"}], "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>Procedure Record</p></div>"}}}, {"fullUrl": "urn:uuid:4b0a80b6-94d7-4776-8ea8-b641f4e44de7", "resource": {"resourceType": "ServiceRequest", "id": "4b0a80b6-94d7-4776-8ea8-b641f4e44de7", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/ServiceRequest"]}, "status": "completed", "intent": "order", "authoredOn": "2025-06-04T05:14:22.851+05:30", "category": [{"coding": [{"system": "http://snomed.info/sct", "code": "396550006", "display": "blood test"}], "text": "(Notes: test notes)"}], "code": {"coding": [{"system": "http://snomed.info/sct", "code": "15220000", "display": "Laboratory test"}], "text": "Laboratory test"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "requester": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "Practitioner"}, "text": {"status": "generated", "div": "<div xmlns='http://www.w3.org/1999/xhtml' lang='en' xml:lang='en'><p>ServiceRequest Record</p></div>"}}}, {"fullUrl": "urn:uuid:0f32e27b-49b5-4da1-9dfe-c8064be7034b", "resource": {"resourceType": "MedicationStatement", "id": "0f32e27b-49b5-4da1-9dfe-c8064be7034b", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationStatement"]}, "status": "completed", "dateAsserted": "2025-06-04T05:14:22.851+05:30", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "134463001", "display": "Telmisartan 20 mg oral tablet"}], "text": "Telmisartan 20 mg oral tablet"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}}}, {"fullUrl": "urn:uuid:1b47767f-68ac-4c85-a6ae-9a6d2e44cfdc", "resource": {"resourceType": "MedicationRequest", "id": "1b47767f-68ac-4c85-a6ae-9a6d2e44cfdc", "meta": {"profile": ["https://nrces.in/ndhm/fhir/r4/StructureDefinition/MedicationRequest"]}, "status": "active", "intent": "order", "authoredOn": "2025-06-03T23:43:27.184Z", "medicationCodeableConcept": {"coding": [{"system": "http://snomed.info/sct", "code": "2377371000189103", "display": "Parawel 250 Mg/5 Ml Oral Suspension"}], "text": "Parawel 250 Mg/5 Ml Oral Suspension"}, "subject": {"reference": "urn:uuid:b5c77529-89fe-4eb1-bedf-0b4333501c4e", "display": "Patient"}, "requester": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "Practitioner"}, "reasonReference": [{"reference": "urn:uuid:4bed1b2f-14b4-4ce0-821a-27ede6cc3276", "display": "Condition"}], "dosageInstruction": [{"text": "test notes only"}], "reasonCode": [{"coding": [{"system": "http://snomed.info/sct", "code": "397991007", "display": "Monitoring Problem"}], "text": "(Duration: 1 Days) (Notes: test notes)"}]}}], "signature": {"type": [{"system": "urn:iso-astm:E1762-95:2013", "code": "1.2.840.10065.1.12.1.1", "display": "Author's Signature"}], "when": "2025-06-04T05:14:22.851+05:30", "who": {"reference": "urn:uuid:dc25fef5-afb0-4b56-9a1b-8ee37883be13", "display": "Practitioner"}, "sigFormat": "image/jpeg", "data": "c2lnbmF0dXJlIGRhdGEgaGVyZQ=="}}